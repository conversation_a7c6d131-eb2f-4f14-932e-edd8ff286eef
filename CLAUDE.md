# Claude Development Guide
## Ultimate Electrical Designer Project

### Document Information
- **Purpose**: Specialized guide for Claude Code sessions on this project
- **Version**: 1.0
- **Date**: July 2025
- **Architecture**: 5-Layer Backend Architecture
- **Methodology**: 5-Phase Implementation Methodology

---

## 1. Project Context & Overview

### 1.1 Application Purpose
The Ultimate Electrical Designer is a professional electrical engineering application built for licensed electrical engineers to design, calculate, and manage electrical systems with IEEE, IEC, and EN standards compliance.

### 1.2 Current Project Status
- **Backend**: 373/373 tests passing (100% pass rate)
- **Frontend**: 66/66 tests passing (100% pass rate)
- **Authentication**: Complete JWT-based system
- **Component Management**: Full CRUD operations implemented
- **Type Safety**: 97.6% MyPy coverage, strict TypeScript mode

### 1.3 Engineering Standards
- **Zero-Tolerance Policy**: No warnings, no technical debt, no incomplete implementations
- **Professional Standards**: IEEE, IEC, EN electrical engineering standards only
- **Quality Metrics**: 90%+ test coverage, 99.9% linting compliance
- **Security**: Zero critical vulnerabilities, comprehensive security validation

---

## 2. Architecture & Technical Stack

### 2.1 Backend Architecture (5-Lay<PERSON> Pattern)

```
server/src/
├── api/                    # Layer 1: API Routes (FastAPI endpoints)
│   ├── v1/                # Versioned API routes
│   │   ├── auth_routes.py # Authentication endpoints
│   │   ├── component_routes.py # Component management
│   │   └── router.py      # Main API router
│   └── main_router.py     # Application router
├── core/                   # Layer 2-4: Business Logic
│   ├── models/            # Layer 2: Data Models (SQLAlchemy)
│   │   ├── auth/          # Authentication models
│   │   ├── general/       # General domain models
│   │   └── base.py        # Base model classes
│   ├── schemas/           # Layer 3: Validation Schemas (Pydantic)
│   │   ├── auth/          # Authentication schemas
│   │   ├── general/       # General domain schemas
│   │   └── base.py        # Base schema classes
│   ├── services/          # Layer 4: Business Logic Services
│   │   ├── auth/          # Authentication services
│   │   ├── general/       # General domain services
│   │   └── dependencies.py # Service dependencies
│   ├── repositories/      # Layer 4: Data Access Layer
│   │   ├── auth/          # Authentication repositories
│   │   ├── general/       # General domain repositories
│   │   └── base_repository.py # Base repository class
│   ├── enums/             # Domain-specific enumerations
│   ├── errors/            # Unified error handling system
│   ├── security/          # Security validation framework
│   └── utils/             # Utility functions and helpers
├── config/                # Layer 5: Configuration
│   └── settings.py        # Application configuration
├── middleware/            # Middleware components
└── main.py               # Application entry point & CLI
```

### 2.2 Frontend Architecture (Component-Based)

```
client/src/
├── app/                   # Next.js App Router
│   ├── (auth)/           # Authentication pages
│   ├── admin/            # Admin dashboard
│   ├── globals.css       # Global styles
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/           # React Components
│   ├── auth/            # Authentication components
│   ├── admin/           # Admin components
│   ├── ui/              # shadcn/ui base components
│   └── common/          # Common reusable components
├── hooks/               # Custom React Hooks
│   ├── api/            # API-specific hooks
│   └── useAuth.ts      # Main authentication hook
├── lib/                # Core Libraries
│   ├── api/            # API client (React Query)
│   ├── auth/           # Authentication utilities
│   └── react-query.tsx # React Query configuration
├── stores/             # Zustand State Management
├── types/              # TypeScript Definitions
└── utils/              # Utility Functions
```

### 2.3 Technology Stack

#### Backend Stack
- **Framework**: FastAPI 0.115+ with Uvicorn ASGI server
- **Database**: SQLAlchemy 2.0+ with PostgreSQL (prod) / SQLite (dev)
- **Authentication**: JWT with python-jose, passlib for password hashing
- **Validation**: Pydantic 2.0+ for request/response validation
- **Testing**: Pytest with 100% real database testing (no mocks)
- **Type Safety**: MyPy with strict type checking
- **Linting**: Ruff for code formatting and linting
- **Scientific**: NumPy, SciPy, Pandas for electrical calculations

#### Frontend Stack
- **Framework**: Next.js 15.3+ with App Router and TypeScript 5.x+
- **Styling**: Tailwind CSS 4.1+ with CSS-in-JS support
- **UI Components**: shadcn/ui (Radix UI primitives + Tailwind)
- **State Management**: 
  - **Server State**: React Query (TanStack Query) 5.x+
  - **Client State**: Zustand 4.x+
- **Forms**: React Hook Form with Zod validation
- **Testing**: Vitest + React Testing Library + Playwright E2E
- **Type Safety**: Strict TypeScript with comprehensive type definitions

---

## 3. Development Patterns & Best Practices

### 3.1 Unified Error Handling Pattern

```python
# Backend: Always use the unified error handling decorator
from core.errors.unified_error_handler import handle_database_errors

@handle_database_errors
async def create_component(component_data: ComponentCreate) -> Component:
    # Implementation with automatic error handling
    pass
```

```typescript
// Frontend: Consistent error handling with React Query
const { data, error, isLoading } = useQuery({
  queryKey: ['components'],
  queryFn: () => apiClient.get('/components'),
  retry: 3,
  retryDelay: 1000
});
```

### 3.2 CRUD Endpoint Factory Pattern

```python
# Backend: Use the CRUD endpoint factory for consistent API endpoints
from core.utils.crud_endpoint_factory import create_crud_endpoints

# Creates standard CRUD endpoints automatically
create_crud_endpoints(
    router=router,
    model=Component,
    schema_create=ComponentCreate,
    schema_update=ComponentUpdate,
    schema_response=ComponentResponse,
    service=component_service
)
```

### 3.3 Performance Monitoring Pattern

```python
# Backend: Apply performance monitoring to critical operations
from core.utils.performance_monitor import monitor_performance

@monitor_performance
async def complex_calculation(data: CalculationInput) -> CalculationResult:
    # Implementation with automatic performance monitoring
    pass
```

### 3.4 Security Validation Pattern

```python
# Backend: Comprehensive security validation
from core.security.input_validators import validate_input

@validate_input
async def process_user_input(input_data: UserInput) -> ProcessedData:
    # Implementation with automatic security validation
    pass
```

### 3.5 Type Safety Patterns

```python
# Backend: Complete type annotations required
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class ComponentService:
    def __init__(self, repository: ComponentRepository) -> None:
        self.repository = repository
    
    async def get_components(self, filters: Optional[Dict[str, Any]] = None) -> List[Component]:
        return await self.repository.get_all(filters=filters)
```

```typescript
// Frontend: Strict TypeScript with comprehensive interfaces
interface ComponentData {
  id: string;
  name: string;
  category: ComponentCategory;
  specifications: ComponentSpecifications;
  createdAt: Date;
}

const ComponentList: React.FC<{ components: ComponentData[] }> = ({ components }) => {
  // Implementation with full type safety
};
```

---

## 4. Database & Data Management

### 4.1 Database Architecture
- **Development**: SQLite database in `server/data/app_dev.db`
- **Production**: PostgreSQL with connection pooling
- **Migrations**: Alembic for database version control
- **Testing**: Real database connections (no mocks policy)

### 4.2 Model Pattern
```python
# Base model with common fields
class BaseModel(SQLAlchemyBase):
    id: Mapped[str] = mapped_column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    class Config:
        arbitrary_types_allowed = True
```

### 4.3 Repository Pattern
```python
# Generic repository with type safety
class BaseRepository(Generic[T]):
    def __init__(self, model: Type[T], session: AsyncSession) -> None:
        self.model = model
        self.session = session
    
    async def get_by_id(self, id: str) -> Optional[T]:
        return await self.session.get(self.model, id)
    
    async def get_all(self, filters: Optional[Dict[str, Any]] = None) -> List[T]:
        # Implementation with filtering support
        pass
```

### 4.4 Database Commands
```bash
# Common database operations
cd server/

# Create migration
poetry run alembic revision --autogenerate -m "description"

# Apply migrations
poetry run python main.py migrate

# Reset database (development only)
poetry run python main.py wipe-database --confirm

# Create admin user
poetry run python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'
```

---

## 5. Authentication & Security

### 5.1 Authentication Flow
1. User authenticates via `/api/v1/auth/login`
2. JWT token generated with user claims
3. Token stored in localStorage via TokenManager
4. ApiClient attaches token to all requests
5. useAuth hook manages authentication state
6. RouteGuard protects authenticated routes

### 5.2 Security Implementation
```python
# Backend: JWT token generation
from core.security.jwt_handler import create_access_token

async def authenticate_user(credentials: UserCredentials) -> AuthResponse:
    user = await user_service.authenticate(credentials)
    if user:
        token = create_access_token(data={"sub": user.id})
        return AuthResponse(access_token=token, user=user)
    raise HTTPException(401, "Invalid credentials")
```

```typescript
// Frontend: Authentication hook
const useAuth = () => {
  const { data: user, isLoading } = useQuery({
    queryKey: ['auth', 'user'],
    queryFn: () => apiClient.get('/auth/me'),
    retry: false,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  const login = useMutation({
    mutationFn: (credentials: LoginCredentials) => 
      apiClient.post('/auth/login', credentials),
    onSuccess: (response) => {
      TokenManager.setToken(response.data.access_token);
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    }
  });

  return { user, isLoading, login };
};
```

### 5.3 Role-Based Access Control
```python
# Backend: RBAC implementation
from enum import Enum

class UserRole(str, Enum):
    ADMIN = "admin"
    ENGINEER = "engineer"
    SENIOR_ENGINEER = "senior_engineer"

@require_role(UserRole.ADMIN)
async def admin_only_endpoint():
    pass
```

---

## 6. Testing Framework & Quality Assurance

### 6.1 Testing Strategy
- **No Mocks Policy**: Real database connections for integration tests
- **5-Phase Testing**: Unit → Integration → API → E2E → Performance
- **Coverage Requirements**: 90%+ for critical modules, 85%+ for high priority
- **Test Categories**: unit, integration, api, database, security, performance

### 6.2 Backend Testing Patterns
```python
# Pytest with real database
@pytest.mark.asyncio
@pytest.mark.integration
async def test_component_creation(async_session: AsyncSession):
    # Arrange
    component_data = ComponentCreate(
        name="Test Component",
        category_id="test-category"
    )
    
    # Act
    component = await component_service.create(component_data)
    
    # Assert
    assert component.name == "Test Component"
    assert component.id is not None
```

### 6.3 Frontend Testing Patterns
```typescript
// Vitest with React Testing Library
describe('ComponentList', () => {
  it('should render components correctly', async () => {
    const mockComponents = [
      { id: '1', name: 'Component 1', category: 'electrical' },
      { id: '2', name: 'Component 2', category: 'mechanical' }
    ];

    render(
      <QueryClient>
        <ComponentList components={mockComponents} />
      </QueryClient>
    );

    expect(screen.getByText('Component 1')).toBeInTheDocument();
    expect(screen.getByText('Component 2')).toBeInTheDocument();
  });
});
```

### 6.4 E2E Testing with Playwright
```typescript
// Authentication flow testing
test.describe('Authentication Flow', () => {
  test('should login successfully', async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="username"]', 'admin');
    await page.fill('[data-testid="password"]', 'Pass123');
    await page.click('[data-testid="login-button"]');
    
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible();
  });
});
```

---

## 7. Development Workflow & Commands

### 7.1 Backend Development Commands
```bash
# From server/ directory
poetry install                              # Install dependencies
poetry run python main.py run --reload     # Development server
poetry run pytest                          # Run all tests
poetry run pytest -v -m unit              # Unit tests only
poetry run pytest -v -m integration       # Integration tests only
poetry run pytest --cov=src --cov-report=html # Coverage report
poetry run mypy src/                       # Type checking
poetry run ruff check .                    # Linting
poetry run ruff format .                   # Code formatting
```

### 7.2 Frontend Development Commands
```bash
# From client/ directory
npm install                                # Install dependencies
npm run dev                               # Development server
npm run build                             # Production build
npm run type-check                        # TypeScript checking
npm run lint                              # ESLint
npm run test                              # Unit tests
npm run test:coverage                     # Coverage report
npm run test:e2e                          # E2E tests
```

### 7.3 Global Development Commands
```bash
# From root directory
make help                                 # Show all commands
make install                              # Install all dependencies
make type-check                           # Full type checking
make test                                 # Run all tests
make lint                                 # Run linting
make clean                                # Clean temporary files
make dev                                  # Start backend server
```

---

## 8. UI Component Library (shadcn/ui)

### 8.1 Component Architecture
The project uses **shadcn/ui** as the primary UI component library, built on:
- **Radix UI**: Headless, accessible component primitives
- **Tailwind CSS**: Utility-first CSS framework
- **Atomic Design**: Components organized as atoms, molecules, organisms

### 8.2 Component Usage Patterns
```typescript
// Base UI components (atoms)
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Composite components (molecules)
const ComponentForm: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Create Component</CardTitle>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <Input placeholder="Component name" />
          <Button type="submit">Create</Button>
        </form>
      </CardContent>
    </Card>
  );
};
```

### 8.3 Theme and Styling
```typescript
// Tailwind CSS configuration with custom electrical engineering theme
const config = {
  theme: {
    extend: {
      colors: {
        primary: '#1E40AF',     // Engineering blue
        secondary: '#DC2626',   // Alert red
        accent: '#059669',      // Success green
        neutral: '#6B7280'      // Professional gray
      }
    }
  }
};
```

---

## 9. Performance & Optimization

### 9.1 Backend Performance
- **Database Optimization**: Query optimization with proper indexing
- **Caching**: Redis for frequently accessed data
- **Connection Pooling**: Efficient database connection management
- **Async Operations**: Full async/await implementation

### 9.2 Frontend Performance
- **Code Splitting**: Route-based code splitting with Next.js
- **Image Optimization**: Next.js Image component with optimization
- **Bundle Analysis**: Regular bundle size monitoring
- **React Query**: Efficient server state management with caching

### 9.3 Monitoring & Analytics
```python
# Backend: Performance monitoring
from core.utils.performance_monitor import monitor_performance

@monitor_performance
async def calculate_electrical_load(data: LoadData) -> LoadResult:
    # Automatic performance tracking
    pass
```

---

## 10. Deployment & DevOps

### 10.1 Environment Configuration
- **Development**: SQLite database, local file storage
- **Production**: PostgreSQL database, cloud storage
- **Environment Variables**: Comprehensive environment configuration
- **Docker**: Containerized deployment with Docker Compose

### 10.2 CI/CD Pipeline
- **Pre-commit Hooks**: Husky + lint-staged for code quality
- **Automated Testing**: Full test suite on every commit
- **Type Checking**: MyPy and TypeScript validation
- **Security Scanning**: Automated vulnerability assessments

### 10.3 Monitoring & Logging
- **Application Monitoring**: Comprehensive logging framework
- **Error Tracking**: Structured error reporting
- **Performance Metrics**: Response time and throughput monitoring
- **Security Monitoring**: Security event logging and alerting

---

## 11. Electrical Engineering Domain

### 11.1 Standards Compliance
- **IEEE Standards**: IEEE-141 (Power System Analysis), IEEE-142 (Grounding)
- **IEC Standards**: IEC-60079 (Explosive Atmospheres), IEC-61508 (Safety)
- **EN Standards**: EN-50110 (Safety), EN-60204 (Machinery Safety)

### 11.2 Calculation Engine
```python
# Electrical calculations with standards compliance
from core.calculations.electrical import ElectricalCalculator

calculator = ElectricalCalculator()
result = await calculator.calculate_load(
    components=components,
    voltage=480,
    power_factor=0.85,
    standard="IEEE-141"
)
```

### 11.3 Professional Documentation
- **Calculation Reports**: IEEE-compliant calculation documentation
- **Compliance Certificates**: Standards compliance verification
- **Professional Seals**: Digital signature support for PE stamps
- **Audit Trails**: Complete documentation of all calculations

---

## 12. Development Guidelines for Claude

### 12.1 Code Quality Standards
- **Zero-Tolerance Policy**: No warnings, no incomplete implementations
- **Type Safety**: 100% type annotations (Python), strict TypeScript
- **Testing**: Real database testing, comprehensive coverage
- **Documentation**: Complete inline documentation for all public APIs

### 12.2 Implementation Approach
- **5-Phase Methodology**: Always follow the established phases
- **Unified Patterns**: Use established error handling and performance patterns
- **Standards Compliance**: Ensure all implementations meet electrical standards
- **Professional Quality**: Engineering-grade attention to detail

### 12.3 Common Tasks
- **API Development**: Use CRUD endpoint factory for consistency
- **Database Operations**: Follow repository pattern with type safety
- **Frontend Components**: Use shadcn/ui components with proper TypeScript
- **Testing**: Write comprehensive tests with real database connections
- **Documentation**: Update relevant documentation with changes

### 12.4 Troubleshooting
- **Database Issues**: Check migration status and connection configuration
- **Type Errors**: Verify MyPy and TypeScript configurations
- **Test Failures**: Ensure proper test database setup
- **Performance Issues**: Check query optimization and caching

---

## 13. Quick Reference

### 13.1 Project Structure
```
ultimate-electrical-designer/
├── server/                 # Backend (Python FastAPI)
├── client/                 # Frontend (Next.js TypeScript)
├── docs/                   # Project documentation
├── Makefile               # Global development commands
├── PRD.md                 # Product requirements
├── PLANNING.md            # Project planning
├── TASKS.md               # Task management
└── CLAUDE.md              # This guide
```

### 13.2 Key Files
- **Backend Entry**: `server/src/main.py`
- **Frontend Entry**: `client/src/app/page.tsx`
- **API Router**: `server/src/api/main_router.py`
- **Auth Hook**: `client/src/hooks/useAuth.ts`
- **Database Models**: `server/src/core/models/`
- **UI Components**: `client/src/components/ui/`

### 13.3 Development Checklist
- [ ] Run tests: `make test`
- [ ] Check types: `make type-check`
- [ ] Run linting: `make lint`
- [ ] Update documentation
- [ ] Verify standards compliance
- [ ] Test with real database
- [ ] Review security implications
- [ ] Check performance impact

---

This guide provides comprehensive context for Claude Code sessions on the Ultimate Electrical Designer project. Always refer to this guide for project-specific patterns, standards, and best practices to ensure consistency with the established engineering-grade development approach.